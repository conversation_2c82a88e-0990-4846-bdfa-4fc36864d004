'use client';
import React, { useEffect } from 'react';
import useDataFetch from '@/hooks/useDataFetch';
import SkinPreview from '../skin/SkinPreview';

const SharedDiarySection = () => {
  const { data, isLoading, error } = useDataFetch({
    queryKey: 'shared-entries',
    endPoint: 'diary/shared-entries?page=1&limit=10&sortBy=createdAt',
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const sharedEntries = data?.items || [];
  console.log('Shared entries:', sharedEntries);
  return (
    <div className="max-w-7xl mx-auto bg-white relative px-5 xl:px-0">
      <div className="py-10">
        <div className="mt-10 mb-7 w-full text-3xl font-semibold">
          Publicly Shared Diary
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {sharedEntries.map((entry, index) => (
          <div key={index}>
            <SkinPreview
              key={index}
              skin={entry.skin.templateContent}
              contentData={{
                subject: entry.title,
                body: entry.content,
                date: entry.entryDate,
              }}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default SharedDiarySection;
